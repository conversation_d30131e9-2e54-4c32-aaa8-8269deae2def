#ifndef __IF__COLLISION_MATH_PLANE_HPP
#define __IF__COLLISION_MATH_PLANE_HPP

// Third-party libraries
#include <glm/glm.hpp>

namespace IronFrost {
  namespace CollisionMath {
    /**
     * Represents a plane in 3D space using the equation: ax + by + cz + d = 0
     * The normal vector (a, b, c) should be normalized for distance calculations
     */
    struct Plane {
      glm::vec3 normal;  // Normal vector (should be normalized)
      float distance;    // Distance from origin along normal

      Plane() : normal(0.0f), distance(0.0f) {} 

      Plane(const glm::vec3& normal, float distance)
        : normal(glm::normalize(normal)), distance(distance) {}

      // Create plane from point and normal
      Plane(const glm::vec3& point, const glm::vec3& normal)
        : normal(glm::normalize(normal)) {
        this->distance = glm::dot(point, this->normal);
      }

      // Create plane from three points
      Plane(const glm::vec3& p1, const glm::vec3& p2, const glm::vec3& p3) {
        glm::vec3 v1 = p2 - p1;
        glm::vec3 v2 = p3 - p1;
        normal = glm::normalize(glm::cross(v1, v2));
        distance = glm::dot(p1, normal);
      }
    };

    // ============================================================================
    // PLANE FUNCTIONS
    // ============================================================================

    /**
     * Calculate the signed distance from a point to a plane
     * Positive distance means the point is on the side of the plane the normal points to
     * Negative distance means the point is on the opposite side
     */
    inline float distanceToPlane(const glm::vec3& point, const Plane& plane) {
      return glm::dot(point, plane.normal) - plane.distance;
    }

    /**
     * Check if a point is above a plane (in the direction of the normal)
     */
    inline bool isPointAbovePlane(const glm::vec3& point, const Plane& plane) {
      return distanceToPlane(point, plane) > 0.0f;
    }

    /**
     * Check if a point is below a plane (opposite to the normal direction)
     */
    inline bool isPointBelowPlane(const glm::vec3& point, const Plane& plane) {
      return distanceToPlane(point, plane) < 0.0f;
    }

    /**
     * Project a point onto a plane
     */
    inline glm::vec3 projectPointOntoPlane(const glm::vec3& point, const Plane& plane) {
      float distance = distanceToPlane(point, plane);
      return point - distance * plane.normal;
    }
  }
}

#endif
