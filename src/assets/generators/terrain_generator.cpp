#include "terrain_generator.hpp"

#include <iostream>

namespace IronFrost {
  std::unique_ptr<TerrainData> TerrainGenerator::generateTerrain(const HeightmapData& heightmapData, float heightScale, float blockSize) {
    auto terrainData = std::make_unique<TerrainData>();
    terrainData->heightmapData = const_cast<HeightmapData*>(&heightmapData);

    auto& meshData = terrainData->meshData;

    const int width = heightmapData.width;
    const int height = heightmapData.height;

    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        float u = static_cast<float>(x) / static_cast<float>(width - 1);
        float v = static_cast<float>(y) / static_cast<float>(height - 1);

        // normalized positions in [-0.5, +0.5], so base mesh is 1x1
        float xpos = (u - 0.5f) * blockSize;
        float zpos = (v - 0.5f) * blockSize;

        float ypos = heightmapData.getHeight(x, y) * heightScale;

        meshData.vertices.emplace_back(Vertex{xpos, ypos, zpos, u, v});
      }
    }

    for (int z = 0; z < height - 1; ++z) {
      for (int x = 0; x < width - 1; ++x) {
        int topLeft = z * width + x;
        int topRight = topLeft + 1;
        int bottomLeft = (z + 1) * width + x;
        int bottomRight = bottomLeft + 1;

        meshData.indices.push_back(topLeft);
        meshData.indices.push_back(bottomLeft);
        meshData.indices.push_back(topRight);

        meshData.indices.push_back(topRight);
        meshData.indices.push_back(bottomLeft);
        meshData.indices.push_back(bottomRight);
      }
    }

    meshData.calculateNormals();
    meshData.calculateTangentsAndBitangents();
    meshData.calculateBounds();

    return terrainData;
  }
}
