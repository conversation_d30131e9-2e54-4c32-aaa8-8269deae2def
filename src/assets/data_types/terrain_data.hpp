#ifndef __IF__ASSET_DATA_TYPE_TERRAIN_HPP
#define __IF__ASSET_DATA_TYPE_TERRAIN_HPP

// Local includes
#include "heightmap_data.hpp"
#include "mesh_data.hpp"

namespace IronFrost {
  struct TerrainData {
    TerrainData() = default;

    // Copy semantics are deleted
    TerrainData(const TerrainData&) = delete;
    TerrainData& operator=(const TerrainData&) = delete;

    // Move semantics are enabled
    TerrainData(TerrainData&&) = default;
    TerrainData& operator=(TerrainData&&) = default;

    HeightmapData* heightmapData{nullptr};
    MeshData meshData;
  };

  struct TerrainParams {
    StringID heightmapName;

    float heightScale{1.0f};
    float blockSize{4.0f};
  };
}

#endif
