#include "scene_loader.hpp"

// C++ standard library
#include <exception>
#include <functional>
#include <memory>
#include <optional>
#include <string>
#include <utility>

// Local includes
#include "../../assets/assets_manager.hpp"
#include "../../gui/widgets/image_widget.hpp"
#include "../../gui/widgets/label_widget.hpp"
#include "../../renderer/renderables_manager.hpp"
#include "../../renderer/renderer.hpp"
#include "../../renderer/resource_manager.hpp"
#include "../../vfs/vfs.hpp"
#include "../scene_graph/scene_graph.hpp"
#include "../collisions/collision_shapes/collision_sphere.hpp"
#include "../collisions/collision_shapes/collision_aabb.hpp"
#include "../scene_data_storage.hpp"

namespace IronFrost {
  SceneNode SceneLoader::loadSceneNode(const IRenderer& renderer, json node) const {
    glm::vec3 position = node.value("position", glm::vec3(0.0F, 0.0F, 0.0F));
    glm::vec3 rotation = node.value("rotation", glm::vec3(0.0F, 0.0F, 0.0F));
    glm::vec3 scale = node.value("scale", glm::vec3(1.0F, 1.0F, 1.0F));

    SceneNode sceneNode(position, glm::quat(rotation), scale);

    if (node.contains("light")) loadLight(sceneNode, node["light"]);
    if (node.contains("object")) tryLoadObject(renderer, sceneNode, node["object"]);
    if (node.contains("model")) tryLoadModel(renderer, sceneNode, node["model"]);

    if (node.contains("collisions")) {
      for (auto& collision : node["collisions"]) {
        tryLoadCollision(sceneNode, collision);
      }
    }

    if (node.contains("children")) {
      for (auto& child : node["children"]) {
        sceneNode.children.push_back(loadSceneNode(renderer, child));
      }
    }

    return sceneNode;
  }

  void SceneLoader::tryLoadObject(const IRenderer& renderer, SceneNode& sceneNode, json object) const {
    IResourceManager& resourceManager = renderer.getResourceManager();
    RenderablesManager& renderablesManager = renderer.getRenderablesManager();

    if (object["name"].is_string()) {
      StringID objectName = StringID(object["name"].get<std::string>());
      glm::vec2 uvTiling = object.value("tiling", glm::vec2(1.0F, 1.0F));

      RenderableObjectID renderableObjectID = renderablesManager.getRenderableObjectID(objectName);
      RenderableObject* renderableObject = renderablesManager.getRenderableObject(renderableObjectID);

      if (renderableObject) {
        std::optional<StringID> meshName = resourceManager.findID<MeshHandle>(renderableObject->meshHandle);
        
        if (meshName) {
          SceneObject sceneObject{ 
            .renderableObjectID = renderableObjectID, 
            .uvTiling = uvTiling,
            .bounds = m_sceneContext.sceneDataStorage.get<CollisionMath::AABB>(meshName.value())
          };

          sceneNode.sceneObject = std::make_optional<SceneObject>(sceneObject);
        }
      }
    }
  }

  void SceneLoader::tryLoadModel(const IRenderer& renderer, SceneNode& sceneNode, const json& model) const {
    IResourceManager& resourceManager = renderer.getResourceManager();
    RenderablesManager& renderablesManager = renderer.getRenderablesManager();

    if (model.is_string()) {
      StringID modelName = StringID(model.get<std::string>());
      const ModelHandle& modelHandle = resourceManager.get<ModelHandle>(modelName);

      RenderableModelID renderableModelID = renderablesManager.createRenderableModel(
        resourceManager.get<ShaderHandle>(DefaultShaders::DEFAULT_SHADER_NAME), modelHandle
      );

      SceneModel sceneModel{ 
        .renderableModelID = renderableModelID, 
        .bounds = m_sceneContext.sceneDataStorage.get<CollisionMath::AABB>(modelName)
      };

      sceneNode.sceneModel = std::make_optional<SceneModel>(sceneModel);
    }
  }

  void SceneLoader::tryLoadCollision(SceneNode& sceneNode, json collision) const {
    std::string type = collision["type"].get<std::string>();

    if (type == "mesh_aabb" && sceneNode.sceneObject) {
      auto aabb = std::make_unique<CollisionAABB>(sceneNode.sceneObject->bounds);
      sceneNode.sceneCollisionShapes.push_back(std::move(aabb));
    } else if (type == "model_aabb" && sceneNode.sceneModel) {
      auto aabb = std::make_unique<CollisionAABB>(sceneNode.sceneModel->bounds);
      sceneNode.sceneCollisionShapes.push_back(std::move(aabb));
    } else if (type == "aabb") {
      glm::vec3 min = collision.value("min", glm::vec3(0.0F, 0.0F, 0.0F));
      glm::vec3 max = collision.value("max", glm::vec3(0.0F, 0.0F, 0.0F));

      CollisionMath::AABB aabb{ min, max };
      sceneNode.sceneCollisionShapes.push_back(std::make_unique<CollisionAABB>(CollisionAABB{aabb}));
    } else if (type == "sphere") {
      glm::vec3 center = collision.value("center", glm::vec3(0.0F, 0.0F, 0.0F));
      float radius = collision.value("radius", 1.0F);

      CollisionMath::Sphere sphere{ center, radius };
      sceneNode.sceneCollisionShapes.push_back(std::make_unique<CollisionSphere>(CollisionSphere{sphere}));
    }
  }

  void SceneLoader::loadLight(SceneNode& sceneNode, json light) const {
    glm::vec3 color = light.value("color", glm::vec3(1.0F, 1.0F, 1.0F));
    float intensity = light["intensity"].get<float>();

    sceneNode.sceneLight = std::make_optional<SceneLight>(SceneLight{ color, intensity });
  }

  std::unique_ptr<ImageWidget> SceneLoader::loadImageWidget(json widget) const {
    std::unique_ptr<ImageWidget> newWidget = std::make_unique<ImageWidget>(StringID(widget["texture"]));

    if(widget.contains("color")) {
      glm::vec4 color = widget.value("color", glm::vec4(1.0F, 1.0F, 1.0F, 1.0F));

      newWidget->setColor(color.rgb());
      newWidget->setAlpha(color.a);
    }

    return newWidget;
  }

  std::unique_ptr<LabelWidget> SceneLoader::loadLabelWidget(json widget) const {
    std::unique_ptr<LabelWidget> newWidget = std::make_unique<LabelWidget>(StringID(widget["font"]), widget["label"]);

    if(widget.contains("color")) {
      glm::vec4 color = widget.value("color", glm::vec4(1.0F, 1.0F, 1.0F, 1.0F));

      newWidget->setColor(color.rgb());
      newWidget->setAlpha(color.a);
    }

    return newWidget;
  }

  SceneLoader::SceneLoader(SceneContext& sceneContext, const std::string& sceneConfigPath) :
    m_sceneContext(sceneContext)
  {
    m_sceneConfig = json::parse(m_sceneContext.vfs.readFile(sceneConfigPath));
  }

  void SceneLoader::loadObjects(const IRenderer& renderer) const {
    if(!m_sceneConfig.contains("objects")) {
      return;
    }

    RenderablesManager& renderablesManager = renderer.getRenderablesManager();

    for (const auto& object : m_sceneConfig["objects"]) {
      StringID name = StringID(object["name"]);

      StringID shaderName = object.contains("shader")
        ? StringID(object["shader"])
        : DefaultShaders::DEFAULT_SHADER_NAME;
      StringID materialName = object.contains("material")
        ? StringID(object["material"])
        : FallbackResources::FALLBACK_MATERIAL_NAME;

      StringID meshName = StringID(object["mesh"]);

      renderablesManager.createRenderableObjectWithName(name, shaderName, meshName, materialName);
    }
  }

  void SceneLoader::loadLighting(SceneLighting& sceneLighting) const {
    if(!m_sceneConfig.contains("lighting")) {
      return;
    }

    for (const auto& light : m_sceneConfig["lighting"]) {
      std::string type = light["type"].get<std::string>();

      if (type == "ambient") {
        glm::vec3 color = light.value("color", glm::vec3(1.0F, 1.0F, 1.0F));
        float intensity = light["intensity"].get<float>();

        sceneLighting.setAmbientLightColor(color);
        sceneLighting.setAmbientLightIntensity(intensity);
      } else if (type == "directional") {
        glm::vec3 direction = light.value("direction", glm::vec3(0.0F, -1.0F, 0.0F));
        glm::vec3 color = light.value("color", glm::vec3(1.0F, 1.0F, 1.0F));
        float intensity = light["intensity"].get<float>();

        sceneLighting.addDirectionalLight(direction, color, intensity);
      } else {
        throw std::runtime_error("Unknown light type: " + type);
      }
    }
  }

  void SceneLoader::loadScene(const IRenderer& renderer, const std::function<void(const StringID name, SceneNode&&)>& callback) const {
    if(!m_sceneConfig.contains("scene")) {
      return;
    }
    auto nodes = m_sceneConfig["scene"];

    for (json::const_iterator it = nodes.begin(); it != nodes.end(); ++it) {
      auto node = it.value();
      auto sceneNode = loadSceneNode(renderer, it.value());

      StringID name = StringID(node["name"]);
      callback(name, std::move(sceneNode));
    }
  }

  void SceneLoader::loadGUI(const std::function<void(const StringID name, std::unique_ptr<Widget> widget)>& callback) const {
    auto guiWidgets = m_sceneConfig["gui"];

    for (const auto& widget : guiWidgets) {
      StringID name = StringID(widget["name"]);

      std::string type = widget["type"].get<std::string>();
      glm::vec2 position = widget.value("position", glm::vec2(0.0F, 0.0F));
      glm::vec2 size = widget.value("size", glm::vec2(1.0F, 1.0F));

      std::unique_ptr<Widget> newWidget;
      if(type == "image") {
        newWidget = loadImageWidget(widget);
      } else if(type == "label") {
        newWidget = loadLabelWidget(widget);
      } else {
        throw std::runtime_error("Unknown widget type: " + type);
      }

      newWidget->setPosition(position);
      newWidget->setSize(size);

      callback(name, std::move(newWidget));
    }
  }

  std::vector<std::string> SceneLoader::getSceneInitScriptContents() const {
    std::vector<std::string> scriptContents;

    if (m_sceneConfig.contains("scripts") && m_sceneConfig["scripts"].contains("init")) {
      auto scriptPaths = m_sceneConfig["scripts"]["init"].get<std::vector<std::string>>();

      for (const auto& scriptPath : scriptPaths) {
        try {
          std::string scriptContent = m_sceneContext.vfs.readFile(scriptPath);
          scriptContents.push_back(scriptContent);
        } catch (const std::exception& e) {
          std::cout << "Error loading scene init script " << scriptPath << ": " << e.what() << '\n';
        }
      }
    }

    return scriptContents;
  }

  std::vector<std::string> SceneLoader::getScenePerFrameScriptContents() const {
    std::vector<std::string> scriptContents;

    if (m_sceneConfig.contains("scripts") && m_sceneConfig["scripts"].contains("perFrame")) {
      auto scriptPaths = m_sceneConfig["scripts"]["perFrame"].get<std::vector<std::string>>();

      for (const auto& scriptPath : scriptPaths) {
        try {
          std::string scriptContent = m_sceneContext.vfs.readFile(scriptPath);
          scriptContents.push_back(scriptContent);
        } catch (const std::exception& e) {
          std::cout << "Error loading scene per-frame script " << scriptPath << ": " << e.what() << '\n';
        }
      }
    }

    return scriptContents;
  }
}
