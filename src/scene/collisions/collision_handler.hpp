#ifndef __IF__COLLISION_HANDLER_HPP
#define __IF__COLLISION_HANDLER_HPP

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "collision_cache.hpp"
#include "collision_shapes/collision_shape.hpp"
#include "collision_shapes/collision_aabb.hpp"
#include "collision_shapes/collision_sphere.hpp"
#include "../../utils/collision_math.hpp"
#include "../scene_graph/scene_node.hpp"

namespace IronFrost {
  class CollisionHandler {
    private:
      CollisionCache& m_collisionCache;

    public:
      CollisionHandler(CollisionCache& collisionCache) : m_collisionCache(collisionCache) {}
      
      glm::vec3 resolveCameraMovement(const Camera& camera, const glm::vec3& desiredPosition) const {
        CollisionMath::Sphere cameraSphere{desiredPosition, camera.getCollisionRadius()};

        for (const auto& candidate : m_collisionCache.getCandidates()) {
          for (const auto& shape : candidate->sceneCollisionShapes) {
            if (shape->intersectsSphere(cameraSphere)) {
              return shape->resolveSphereCollision(cameraSphere);
            }
          }
        }

        return desiredPosition;
      }
  };
}

#endif
