#ifndef __IF__COLLISION_SHAPE_HPP
#define __IF__COLLISION_SHAPE_HPP

// Local includes
#include "../../../utils/collision_math.hpp"

namespace IronFrost {
  class CollisionAABB;
  class CollisionSphere;
  
  class CollisionShape {
    public:
      virtual ~CollisionShape() = default;

      virtual void update(const glm::mat4& transform) = 0;

      virtual CollisionMath::AABB getWorldAABB() const = 0;

      virtual bool intersects(const CollisionShape& other) const = 0;
      virtual bool intersectsSphere(const CollisionSphere& sphere) const = 0;
      virtual bool intersectsAABB(const CollisionAABB& aabb) const = 0;

      virtual glm::vec3 resolveSphereCollision(const CollisionMath::Sphere& sphere) {
        return sphere.center;
      }

      virtual glm::vec3 resolveAABBCollision(const CollisionMath::AABB& aabb) {
        return aabb.getCenter();
      }
  };
}

#endif
