#ifndef __IF__SCENE_NODE_HPP
#define __IF__SCENE_NODE_HPP

// C++ standard library
#include <functional>
#include <optional>
#include <vector>

// Third-party libraries
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/quaternion.hpp>

// Local includes
#include "scene_entities.hpp"

namespace IronFrost {
  struct SceneNode {
    private:
      glm::vec3 m_position{glm::vec3(0.0f)};
      glm::quat m_rotation{glm::identity<glm::quat>()};
      glm::vec3 m_scale{glm::vec3(1.0f)};

      mutable glm::mat4 m_transform{glm::mat4(1.0f)};
      mutable bool m_transformDirty{true};

      void markDirty() {
        m_transformDirty = true;
        for (auto &child : children) {
          child.markDirty();
        }
      }
      
      glm::mat4 computeLocalTransform() const {
        glm::mat4 translationMatrix = glm::translate(glm::mat4(1.0f), m_position);
        glm::mat4 rotationMatrix = glm::mat4_cast(m_rotation);
        glm::mat4 scalingMatrix = glm::scale(glm::mat4(1.0f), m_scale);

        return translationMatrix * rotationMatrix * scalingMatrix;
      }
      
    public:
      SceneNode() = default;
      SceneNode(glm::vec3 _position, glm::quat _rotation, glm::vec3 _scale) : 
        m_position(_position), 
        m_rotation(_rotation), 
        m_scale(_scale) 
      {}

      const glm::vec3& getPosition() const { return m_position; }
      const glm::quat& getRotation() const { return m_rotation; }
      const glm::vec3& getScale() const { return m_scale; }

      void setPosition(const glm::vec3& _position) { 
        m_position = _position; 
        markDirty();
      }

      void setRotation(const glm::quat& _rotation) { 
        m_rotation = _rotation; 
        markDirty();
      }

      void setScale(const glm::vec3& _scale) { 
        m_scale = _scale;
        markDirty();
      }

      glm::mat4 getTransform(const glm::mat4& parentTransform = glm::mat4(1.0f)) const {
        if (m_transformDirty) {
          m_transform = parentTransform * computeLocalTransform();
          m_transformDirty = false;
        }
        
        return m_transform;
      }
      
      std::optional<SceneObject> sceneObject{std::nullopt};
      std::optional<SceneModel> sceneModel{std::nullopt};
      std::optional<SceneLight> sceneLight{std::nullopt};

      std::vector<std::unique_ptr<SceneCollisionShape>> sceneCollisionShapes{};

      bool visible{true};

      std::vector<SceneNode> children;

      void traverse(const glm::mat4& parentTransform, std::function<void(SceneNode&, const glm::mat4&)> _callback) {
        glm::mat4 worldTransform = getTransform(parentTransform);

        _callback(*this, worldTransform);

        for (auto &child : children) {
          child.traverse(worldTransform, _callback);
        }
      }

      void traverseDirty(const glm::mat4& parentTransform, std::function<void(SceneNode&, const glm::mat4&)> _callback) {
        if (m_transformDirty) {
          glm::mat4 worldTransform = parentTransform * computeLocalTransform();
          _callback(*this, worldTransform);
        }

        for (auto &child : children) {
          child.traverseDirty(getTransform(parentTransform), _callback);
        }
      }

      std::optional<CollisionMath::AABB> getWorldBounds(const glm::mat4& worldTransform = glm::mat4(1.0f)) const {
        if (sceneObject) {
          return CollisionMath::transformAABB(sceneObject->bounds, worldTransform);
        } 
        if (sceneModel) {
          return CollisionMath::transformAABB(sceneModel->bounds, worldTransform);
        }
    
        return std::nullopt;
      }
  };
}

#endif
